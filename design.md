# 知识图谱构建系统设计文档

## 1. 系统概述

本系统是一个基于大语言模型的知识图谱构建平台，支持从多种格式的文档中提取实体和关系，构建结构化的知识图谱。系统采用前后端分离架构，提供Web界面和RESTful API，支持批量处理、整合处理和图谱更新等核心功能。

### 1.1 核心特性
- **多格式文档支持**: TXT、PDF、DOCX、DOC、XLSX、XLS、CSV、JSON
- **智能实体抽取**: 基于LLM的实体和关系识别
- **实体消歧**: LLM驱动的实体变体识别和合并
- **批量处理**: 并行处理多个文件，生成独立图谱
- **整合处理**: 渐进式构建统一知识图谱
- **图谱更新**: 基于现有图谱的增量更新
- **可视化展示**: 交互式图谱可视化界面

### 1.2 技术栈
- **后端**: Python 3.11 + FastAPI + Pydantic
- **前端**: React 18 + TypeScript + Vite
- **LLM服务**: 支持OpenAI兼容API (qwen2.5-32b-instruct-int4)
- **图谱可视化**: D3.js + Force-directed layout
- **文档处理**: PyPDF2、python-docx、openpyxl
- **部署**: Docker + Docker Compose

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   后端API       │    │   LLM服务       │
│   (React)       │◄──►│   (FastAPI)     │◄──►│   (OpenAI API)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   文件存储      │
                       │   (本地文件系统) │
                       └─────────────────┘
```

### 2.2 系统整体处理流程
```mermaid
flowchart TD
    A[用户上传文件] --> B{处理模式选择}
    B -->|批量独立| C[批量处理API]
    B -->|整合处理| D[整合处理API]
    B -->|图谱更新| E[图谱更新API]

    C --> F[FileService文件处理]
    D --> F
    E --> F

    F --> G[文本提取和预处理]
    G --> H[TextChunk分块]
    H --> I[LLMService并发调用]
    I --> J[实体预处理服务]
    J --> K[EntityDisambiguationService]
    K --> L[KnowledgeGraphService]
    L --> M[ProgressTracker进度跟踪]
    M --> N[图谱保存和清理]
    N --> O[返回处理结果]
```

### 2.3 后端服务架构
```
app/
├── api/                    # API路由层
│   ├── files.py           # 文件处理API
│   ├── knowledge_graph.py # 知识图谱API
│   └── progress.py        # 进度跟踪API
├── core/                  # 核心配置
│   └── config.py          # 系统配置
├── models/                # 数据模型
│   ├── file_models.py     # 文件相关模型
│   └── kg_models.py       # 知识图谱模型
├── services/              # 业务服务层
│   ├── file_service.py    # 文件处理服务
│   ├── llm_service.py     # LLM交互服务
│   ├── knowledge_graph_service.py  # 知识图谱服务
│   ├── entity_disambiguation_service.py  # 实体消歧服务
│   └── progress_tracker.py  # 进度跟踪服务
└── utils/                 # 工具函数
    ├── file_utils.py      # 文件工具
    └── text_utils.py      # 文本处理工具
```

## 3. 核心功能设计

### 3.1 批量处理功能

#### 3.1.1 功能描述
批量处理功能允许用户同时上传多个文件，系统并行处理每个文件，为每个文件生成独立的知识图谱。

#### 3.1.2 处理流程
```mermaid
flowchart TD
    A[用户上传多个文件] --> B[文件验证和存储]
    B --> C{处理模式选择}
    C -->|批量独立处理| D[遍历文件列表]
    D --> E[单文件处理]
    E --> F[文本提取和预处理]
    F --> G[文本语义分块]
    G --> H[并发LLM实体关系抽取]
    H --> I[实体预处理服务]
    I --> J[增强实体消歧]
    J --> K[关系ID更新]
    K --> L[模糊匹配处理]
    L --> M[构建独立图谱]
    M --> N[保存图谱文件]
    N --> O[清理上传文件]
    O --> P{还有文件?}
    P -->|是| E
    P -->|否| Q[返回处理结果]
```

#### 3.1.3 核心工具和函数
- **API端点**: `POST /api/files/batch-process`
- **核心服务**: `FileService`, `LLMService`, `KnowledgeGraphService`, `EntityDisambiguationService`
- **关键函数**:
  - `batch_process_files()`: 批量处理API入口
  - `process_file()`: 单文件处理逻辑
  - `build_knowledge_graph_with_preprocessing()`: 带预处理的图谱构建
  - `build_knowledge_graph_from_chunks()`: 从文本块构建图谱
  - `extract_entities_and_relations_batch()`: 批量并发实体关系抽取
  - `preprocess_entities_batch()`: 批量实体预处理
  - `_enhanced_entity_disambiguation()`: 增强实体消歧
  - `_update_relation_entity_ids_with_mapping()`: 关系ID映射更新
  - `fuzzy_match_entities()`: 模糊匹配处理

#### 3.1.4 并发控制
- 使用`asyncio.Semaphore`控制LLM API并发数量
- 默认最大并发数: 8个文本块
- 支持动态调整并发参数

### 3.2 整合处理功能

#### 3.2.1 功能描述
整合处理功能将多个文件的内容合并，构建统一的知识图谱，通过渐进式合并和智能实体消歧，生成高质量的综合图谱。

#### 3.2.2 处理流程
```mermaid
flowchart TD
    A[用户上传多个文件] --> B[文件验证和存储]
    B --> C[创建主进度跟踪任务]
    C --> D{文件数量检查}
    D -->|单文件| E[直接构建图谱]
    D -->|多文件| F[渐进式构建流程]
    F --> G[从第一个文件构建基础图谱]
    G --> H[文本预处理和分块]
    H --> I[并发LLM实体关系抽取]
    I --> J[实体预处理服务]
    J --> K[增强实体消歧]
    K --> L[构建基础图谱]
    L --> M{还有文件?}
    M -->|是| N[逐个合并剩余文件]
    N --> O[构建新文件临时图谱]
    O --> P[智能合并重复实体]
    P --> Q[更新关系中的实体ID]
    Q --> R[增强关系发现]
    R --> S[创建合并后图谱]
    S --> M
    M -->|否| T[最终优化和清理]
    T --> U[保存统一图谱]
    U --> V[清理临时文件]
    E --> U
```

#### 3.2.3 核心工具和函数
- **API端点**: `POST /api/files/batch-process-integrated`
- **核心服务**: `KnowledgeGraphService`, `EntityDisambiguationService`, `LLMService`
- **关键函数**:
  - `batch_process_files_integrated()`: 整合处理API入口
  - `build_knowledge_graph_from_multiple_files()`: 多文件渐进式图谱构建
  - `_merge_file_into_kg()`: 单文件合并到现有图谱
  - `_merge_duplicate_entities()`: 智能重复实体合并
  - `_update_relation_entity_ids()`: 关系实体ID更新
  - `_enhance_relations_after_merge()`: 合并后关系增强
  - `find_entity_variants_with_llm()`: LLM驱动实体变体识别
  - `llm_based_entity_similarity()`: LLM实体相似度判断
  - `merge_entity_variants()`: 实体变体合并
  - `merge_multiple_texts_for_kg()`: LLM文本整合(回退方案)

#### 3.2.4 渐进式合并策略
1. **基础图谱构建**: 从第一个文件构建初始图谱
2. **逐步合并**: 依次将其他文件内容合并到图谱中
3. **实体对齐**: 使用LLM识别不同文件中的相同实体
4. **关系增强**: 从实体描述中推理隐含关系
5. **最终优化**: 清理冗余信息，优化图谱结构

### 3.3 图谱更新功能

#### 3.3.1 功能描述
图谱更新功能允许用户基于现有知识图谱添加新的文档内容，通过智能合并和实体消歧，生成更新后的图谱版本。

#### 3.3.2 处理流程
```mermaid
flowchart TD
    A[选择现有图谱] --> B[上传新文件]
    B --> C[文件验证和存储]
    C --> D[加载现有图谱数据]
    D --> E[文本提取和预处理]
    E --> F[构建新内容临时图谱]
    F --> G[文本分块和LLM抽取]
    G --> H[实体预处理服务]
    H --> I[合并原图谱和新图谱]
    I --> J[智能实体合并]
    J --> K[LLM实体相似度判断]
    K --> L[实体变体识别和合并]
    L --> M[更新关系中的实体ID]
    M --> N[增强关系发现]
    N --> O[创建新图谱版本]
    O --> P[保存更新后图谱]
    P --> Q[清理上传文件]
    Q --> R[返回更新结果统计]
```

#### 3.3.3 核心工具和函数
- **API端点**: `POST /api/kg/{kg_id}/update`
- **核心服务**: `KnowledgeGraphService`, `EntityDisambiguationService`, `FileService`
- **关键函数**:
  - `update_knowledge_graph()`: 图谱更新API入口
  - `load_knowledge_graph()`: 加载现有图谱数据
  - `build_knowledge_graph_with_preprocessing()`: 新文件图谱构建
  - `_merge_duplicate_entities()`: 智能重复实体合并
  - `_update_relation_entity_ids()`: 关系实体ID更新
  - `_enhance_relations_after_merge()`: 合并后关系增强
  - `llm_based_entity_similarity()`: LLM实体相似度判断
  - `find_entity_variants_with_llm()`: LLM实体变体识别
  - `save_knowledge_graph()`: 保存更新后图谱
  - `delete_file()`: 清理上传文件

#### 3.3.4 智能合并策略
1. **实体匹配**: 使用LLM判断新旧实体是否为同一实体
2. **信息融合**: 合并实体的描述、属性等信息
3. **关系更新**: 更新涉及合并实体的所有关系
4. **冲突解决**: 基于置信度和信息完整性解决冲突
5. **版本管理**: 保留原图谱，生成新版本

## 4. 实体消歧服务设计

### 4.1 多层次相似度计算
- **基础相似度**: 名称、类型、描述的字符串相似度
- **LLM语义相似度**: 使用大语言模型进行语义级判断
- **综合评分**: 结合多个维度的相似度分数

### 4.2 实体消歧处理流程
```mermaid
flowchart TD
    A[实体列表] --> B[基础相似度筛选]
    B --> C[生成候选实体对]
    C --> D{LLM消歧开关}
    D -->|启用| E[LLM语义相似度判断]
    D -->|禁用| F[基础相似度阈值过滤]
    E --> G[批量LLM调用]
    G --> H[解析JSON响应]
    H --> I[置信度阈值过滤]
    I --> J[确认实体变体对]
    F --> J
    J --> K[构建实体变体组]
    K --> L[选择主实体]
    L --> M[合并实体信息]
    M --> N[建立ID映射]
    N --> O[返回合并结果]
```

### 4.3 实体变体识别算法
```python
async def find_entity_variants_with_llm(entities: List[Entity]) -> Dict[str, List[Entity]]:
    # 1. 基础筛选: 快速过滤明显不相关的实体对
    candidates = []
    for i, entity1 in enumerate(entities):
        for j, entity2 in enumerate(entities[i+1:], i+1):
            basic_similarity = calculate_entity_similarity(entity1, entity2)
            if basic_similarity >= 0.3:  # 降低初筛阈值
                candidates.append((entity1, entity2, basic_similarity))

    # 2. LLM精确判断: 对候选实体对进行语义级判断
    confirmed_pairs = []
    for entity1, entity2, basic_sim in candidates:
        llm_similarity = await llm_based_entity_similarity(entity1, entity2)
        if llm_similarity >= LLM_ENTITY_CONFIDENCE_THRESHOLD:
            confirmed_pairs.append((entity1, entity2, llm_similarity))

    # 3. 构建实体组: 基于确认的实体对构建变体组
    return build_entity_groups(confirmed_pairs)
```

### 4.4 LLM实体判断提示词
```
请判断以下两个实体是否指向同一个真实世界的对象（即是否为同一实体的不同表述）。

实体1:
- 名称: {entity1.name}
- 类型: {entity1.type or "未知"}
- 描述: {entity1.description or "无描述"}

实体2:
- 名称: {entity2.name}
- 类型: {entity2.type or "未知"}
- 描述: {entity2.description or "无描述"}

判断标准:
1. 是否指向同一个人、组织、地点、概念等
2. 是否为同一实体的不同名称、简称、全称等
3. 是否为同一实体在不同语言或表述方式下的形式

请返回JSON格式的结果:
{
    "is_same_entity": true/false,
    "confidence": 0.0-1.0,
    "reason": "判断理由"
}

只返回JSON，不要其他内容。
```

## 5. 文件处理服务设计

### 5.1 支持的文件格式
- **文本文件**: .txt
- **PDF文档**: .pdf (使用PyPDF2)
- **Word文档**: .docx, .doc (使用python-docx)
- **Excel表格**: .xlsx, .xls (使用openpyxl)
- **CSV文件**: .csv (使用pandas)
- **JSON文件**: .json

### 5.2 文本预处理流程
```mermaid
flowchart TD
    A[上传文件] --> B[文件格式验证]
    B --> C[文件大小检查]
    C --> D{文件类型判断}
    D -->|TXT| E[直接读取文本]
    D -->|PDF| F[PyPDF2提取文本]
    D -->|DOCX| G[python-docx提取文本]
    D -->|XLSX| H[openpyxl提取文本]
    D -->|CSV| I[pandas读取数据]
    E --> J[文本清理和格式化]
    F --> J
    G --> J
    H --> J
    I --> J
    J --> K[语义边界分割]
    K --> L[块大小优化]
    L --> M[生成TextChunk对象]
    M --> N[返回文本块列表]
```

### 5.3 核心工具函数
```python
class FileService:
    async def extract_text_from_file(self, file_info: FileInfo) -> str:
        """根据文件类型提取文本内容"""
        
    def split_text_into_chunks(self, text: str, chunk_size: int = 2000) -> List[TextChunk]:
        """将文本分割为适合LLM处理的块"""
        
    async def create_temp_file(self, content: str, prefix: str = "temp") -> str:
        """创建临时文件"""
        
    async def delete_file(self, file_path: str) -> bool:
        """删除文件"""
```

## 6. LLM服务设计

### 6.1 LLM并发处理流程
```mermaid
flowchart TD
    A[文本块列表] --> B{并行处理开关}
    B -->|启用| C[批量并发处理]
    B -->|禁用| D[串行处理模式]
    C --> E[信号量控制并发数]
    E --> F[创建异步任务组]
    F --> G[并发调用LLM API]
    G --> H[实体预处理服务]
    H --> I[合并批量结果]
    D --> J[逐个处理文本块]
    J --> K[单次LLM调用]
    K --> L[单次预处理]
    L --> M{还有文本块?}
    M -->|是| J
    M -->|否| N[返回处理结果]
    I --> N
```

### 6.2 API配置
- **API基础URL**: 支持OpenAI兼容接口
- **默认模型**: qwen2.5-32b-instruct-int4
- **备用模型**: doubao-seed-1.6
- **并发控制**: 使用信号量限制并发请求数(默认8个)
- **重试机制**: 指数退避重试策略(最多3次)

### 6.2 实体关系抽取提示词
```
你是一个专业的知识图谱构建专家。请从以下文本中提取实体和关系。

文本内容：
{text}

请按照以下JSON格式返回结果：
{
    "entities": [
        {
            "name": "实体名称",
            "type": "实体类型",
            "description": "实体描述"
        }
    ],
    "relations": [
        {
            "source": "源实体名称",
            "target": "目标实体名称",
            "relation": "关系类型",
            "description": "关系描述"
        }
    ]
}

注意事项：
1. 实体类型包括：人物、地点、组织、概念、事件、产品、时间等
2. 关系要明确、具体，避免过于抽象
3. 确保实体名称在关系中的一致性
4. 只返回JSON格式，不要其他内容
```

### 6.3 核心服务函数
```python
class LLMService:
    async def extract_entities_and_relations(self, text: str) -> Tuple[List[Entity], List[Relation]]:
        """从文本中提取实体和关系"""
        
    async def merge_multiple_texts_for_kg(self, texts: List[str], filenames: List[str]) -> str:
        """合并多个文本用于知识图谱构建"""
        
    async def classify_entity_type(self, entity_name: str, context: str = "") -> str:
        """实体类型分类"""
        
    async def summarize_text(self, text: str) -> str:
        """文本摘要"""
```

## 7. 进度跟踪服务设计

### 7.1 任务状态管理
- **PENDING**: 任务创建但未开始
- **RUNNING**: 任务正在执行
- **COMPLETED**: 任务成功完成
- **FAILED**: 任务执行失败

### 7.2 进度信息结构
```python
@dataclass
class ProgressInfo:
    task_id: str
    task_name: str
    status: TaskStatus
    current: int
    total: int
    percentage: float
    start_time: float
    end_time: Optional[float] = None
    error_message: Optional[str] = None
```

### 7.3 核心功能
- **任务创建**: 创建新的进度跟踪任务
- **进度更新**: 实时更新任务进度
- **状态查询**: 查询任务当前状态
- **回调通知**: 支持进度变化回调
- **任务清理**: 自动清理过期任务

## 8. 数据模型设计

### 8.1 知识图谱模型
```python
class Entity(BaseModel):
    id: str
    name: str
    type: Optional[str] = None
    description: Optional[str] = None
    properties: Dict[str, Any] = Field(default_factory=dict)

class Relation(BaseModel):
    id: str
    source_entity: str
    target_entity: str
    relation_type: str
    confidence: Optional[float] = None
    properties: Dict[str, Any] = Field(default_factory=dict)

class KnowledgeGraph(BaseModel):
    id: str
    name: str
    entities: List[Entity]
    relations: List[Relation]
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime
    updated_at: datetime
```

### 8.2 文件处理模型
```python
class FileInfo(BaseModel):
    id: str
    filename: str
    original_name: str
    file_type: FileType
    file_size: int
    file_path: str
    status: ProcessStatus

class TextChunk(BaseModel):
    id: str
    content: str
    start_position: int
    end_position: int
    metadata: Dict[str, Any] = Field(default_factory=dict)
```

## 9. 配置参数

### 9.1 LLM配置
```python
LLM_API_URL = "https://gateway.chat.sensedeal.vip/v1"
LLM_MODEL = "qwen2.5-32b-instruct-int4"
LLM_MAX_RETRIES = 3
LLM_RETRY_DELAY = 1.0
LLM_TIMEOUT = 60.0
```

### 9.2 处理参数
```python
MAX_CONCURRENT_CHUNKS = 8
CHUNK_SIZE = 2000
ENTITY_SIMILARITY_THRESHOLD = 0.7
LLM_ENTITY_CONFIDENCE_THRESHOLD = 0.7
FUZZY_MATCH_THRESHOLD = 0.8
```

### 9.3 文件配置
```python
MAX_FILE_SIZE = 52428800  # 50MB
ALLOWED_EXTENSIONS = ['.txt', '.pdf', '.docx', '.doc', '.xlsx', '.xls', '.csv', '.json']
UPLOAD_DIR = "data/uploads"
KG_STORAGE_DIR = "data/knowledge_graph"
```

## 10. 部署和运维

### 10.1 Docker部署
```yaml
version: '3.8'
services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - LLM_API_KEY=${LLM_API_KEY}
      
  frontend:
    build: ./frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
```

### 10.2 性能优化
- **并发控制**: 合理设置LLM API并发数
- **缓存机制**: 缓存常用的实体类型分类结果
- **内存管理**: 及时清理临时文件和缓存
- **错误恢复**: 实现任务失败重试机制

### 10.3 监控和日志
- **结构化日志**: 使用loguru记录详细的操作日志
- **性能监控**: 监控LLM API调用延迟和成功率
- **错误追踪**: 记录和分析处理失败的原因
- **资源监控**: 监控CPU、内存、磁盘使用情况

这个设计文档提供了构建知识图谱系统的完整技术方案，涵盖了批量处理、整合处理、图谱更新等核心功能的详细实现方案，以及所需的工具和函数。开发者可以根据这个设计文档搭建完整的知识图谱构建系统。
